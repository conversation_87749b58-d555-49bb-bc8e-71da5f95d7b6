#!/usr/bin/env python3
"""
🚀 Clara Backend - Script de démarrage serveur Windows
Démarre le backend Clara sur un serveur Windows avec configuration optimisée
"""

import os
import sys
import uvicorn
import logging
import argparse
from pathlib import Path

# Configuration par défaut
DEFAULT_HOST = "0.0.0.0"  # Accepter toutes les connexions
DEFAULT_PORT = 8000
DEFAULT_WORKERS = 1  # Pour Windows, 1 worker est recommandé

def setup_logging(log_level="INFO"):
    """Configuration du logging"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('clara_backend.log')
        ]
    )

def check_dependencies():
    """Vérifier les dépendances critiques"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlite3',
        'requests'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ Packages manquants: {', '.join(missing)}")
        print("Installez avec: pip install -r requirements.txt")
        return False
    
    print("✅ Toutes les dépendances sont installées")
    return True

def create_service_config():
    """Créer un fichier de configuration pour service Windows"""
    config = {
        "host": os.getenv("CLARA_HOST", DEFAULT_HOST),
        "port": int(os.getenv("CLARA_PORT", DEFAULT_PORT)),
        "log_level": os.getenv("CLARA_LOG_LEVEL", "INFO"),
        "workers": int(os.getenv("CLARA_WORKERS", DEFAULT_WORKERS)),
        "reload": os.getenv("CLARA_RELOAD", "false").lower() == "true"
    }
    
    return config

def main():
    parser = argparse.ArgumentParser(description="Clara Backend Server")
    parser.add_argument("--host", default=DEFAULT_HOST, help="Host à écouter")
    parser.add_argument("--port", type=int, default=DEFAULT_PORT, help="Port à écouter")
    parser.add_argument("--workers", type=int, default=DEFAULT_WORKERS, help="Nombre de workers")
    parser.add_argument("--log-level", default="INFO", help="Niveau de log")
    parser.add_argument("--reload", action="store_true", help="Auto-reload en développement")
    parser.add_argument("--production", action="store_true", help="Mode production")
    
    args = parser.parse_args()
    
    # Configuration du logging
    setup_logging(args.log_level)
    logger = logging.getLogger("clara-server")
    
    # Vérifier les dépendances
    if not check_dependencies():
        sys.exit(1)
    
    # Configuration
    config = create_service_config()
    
    # Override avec les arguments CLI
    if args.host != DEFAULT_HOST:
        config["host"] = args.host
    if args.port != DEFAULT_PORT:
        config["port"] = args.port
    if args.workers != DEFAULT_WORKERS:
        config["workers"] = args.workers
    if args.log_level != "INFO":
        config["log_level"] = args.log_level
    if args.reload:
        config["reload"] = True
    
    # Mode production
    if args.production:
        config["reload"] = False
        config["log_level"] = "WARNING"
        logger.info("🚀 Mode production activé")
    
    logger.info(f"🚀 Démarrage Clara Backend Server")
    logger.info(f"📡 Host: {config['host']}")
    logger.info(f"🔌 Port: {config['port']}")
    logger.info(f"👥 Workers: {config['workers']}")
    logger.info(f"📝 Log Level: {config['log_level']}")
    
    try:
        # Démarrer le serveur
        uvicorn.run(
            "main:app",
            host=config["host"],
            port=config["port"],
            workers=config["workers"],
            log_level=config["log_level"].lower(),
            reload=config["reload"],
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("🛑 Arrêt du serveur demandé")
    except Exception as e:
        logger.error(f"❌ Erreur serveur: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

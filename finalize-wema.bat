@echo off
echo ========================================
echo      WeMa IA - Finalisation v1.0
echo ========================================
echo.
echo 🔧 Verification de l'environnement...

REM Verifier Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python non trouve
    echo 📦 Installez Python depuis python.org
    pause
    exit /b 1
)
echo ✅ Python detecte

REM Verifier les dependances
echo 🔧 Installation des dependances...
cd backend
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ❌ Erreur installation dependances
    pause
    exit /b 1
)
echo ✅ Dependances installees

REM Test du backend
echo 🔧 Test du backend...
timeout /t 2 /nobreak >nul
python -c "import main; print('Backend OK')" >nul 2>&1
if errorlevel 1 (
    echo ❌ Erreur backend
    pause
    exit /b 1
)
echo ✅ Backend valide

cd ..

REM Verification frontend
echo 🔧 Verification frontend...
if not exist "frontend\index.html" (
    echo ❌ Frontend manquant
    pause
    exit /b 1
)
echo ✅ Frontend present

echo.
echo ========================================
echo      ✅ WeMa IA PRET !
echo ========================================
echo.
echo 🚀 Pour demarrer: start-wema-new.bat
echo 🌐 Interface: http://localhost:8080
echo 🔧 API: http://localhost:5001
echo.
echo 📋 Fonctionnalites:
echo   ✅ Chat IA avec LM Studio Central
echo   ✅ Recherche Internet (SearXNG Central)
echo   ✅ Upload et OCR de documents
echo   ✅ Interface en francais
echo.
echo Version: 1.0.0 Portable
echo Date: 2025-07-09
echo.
pause

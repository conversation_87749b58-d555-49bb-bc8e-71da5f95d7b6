import{s as de}from"./chunk-SKB7J2MH-DOkrMxx7.js";import{_ as x,s as ue,g as pe,x as he,v as fe,a as ge,b as me,H as ae,I as ne,M as ye,e as Se,l as Q,a_ as R,d as K,a$ as ve,B as xe,K as be}from"./index-p8SKoq5x.js";import{p as we}from"./chunk-353BL4L5-DkxX1B8f.js";import{I as Ce}from"./chunk-AACKK3MU-Ou94eVbU.js";import{p as Le}from"./treemap-6Y5VK53G-BeWSxp6O.js";import{b as O}from"./defaultLocale-C4B-KCzX.js";import{o as J}from"./ordinal-Cboi1Yqb.js";import"./vendor-BEryHLmj.js";import"./pdfjs-CcP0jMWS.js";import"./_baseUniq-CAPZjJDQ.js";import"./_basePickBy-BVilwHf_.js";import"./clone-2yJsuTP8.js";import"./init-Gi6I4Gst.js";function $e(e){var a=0,l=e.children,n=l&&l.length;if(!n)a=1;else for(;--n>=0;)a+=l[n].value;e.value=a}function Te(){return this.eachAfter($e)}function Fe(e,a){let l=-1;for(const n of this)e.call(a,n,++l,this);return this}function Ne(e,a){for(var l=this,n=[l],i,r,d=-1;l=n.pop();)if(e.call(a,l,++d,this),i=l.children)for(r=i.length-1;r>=0;--r)n.push(i[r]);return this}function ke(e,a){for(var l=this,n=[l],i=[],r,d,p,g=-1;l=n.pop();)if(i.push(l),r=l.children)for(d=0,p=r.length;d<p;++d)n.push(r[d]);for(;l=i.pop();)e.call(a,l,++g,this);return this}function Ae(e,a){let l=-1;for(const n of this)if(e.call(a,n,++l,this))return n}function _e(e){return this.eachAfter(function(a){for(var l=+e(a.data)||0,n=a.children,i=n&&n.length;--i>=0;)l+=n[i].value;a.value=l})}function ze(e){return this.eachBefore(function(a){a.children&&a.children.sort(e)})}function Me(e){for(var a=this,l=Ve(a,e),n=[a];a!==l;)a=a.parent,n.push(a);for(var i=n.length;e!==l;)n.splice(i,0,e),e=e.parent;return n}function Ve(e,a){if(e===a)return e;var l=e.ancestors(),n=a.ancestors(),i=null;for(e=l.pop(),a=n.pop();e===a;)i=e,e=l.pop(),a=n.pop();return i}function Pe(){for(var e=this,a=[e];e=e.parent;)a.push(e);return a}function Be(){return Array.from(this)}function Re(){var e=[];return this.eachBefore(function(a){a.children||e.push(a)}),e}function De(){var e=this,a=[];return e.each(function(l){l!==e&&a.push({source:l.parent,target:l})}),a}function*We(){var e=this,a,l=[e],n,i,r;do for(a=l.reverse(),l=[];e=a.pop();)if(yield e,n=e.children)for(i=0,r=n.length;i<r;++i)l.push(n[i]);while(l.length)}function ee(e,a){e instanceof Map?(e=[void 0,e],a===void 0&&(a=He)):a===void 0&&(a=Ie);for(var l=new U(e),n,i=[l],r,d,p,g;n=i.pop();)if((d=a(n.data))&&(g=(d=Array.from(d)).length))for(n.children=d,p=g-1;p>=0;--p)i.push(r=d[p]=new U(d[p])),r.parent=n,r.depth=n.depth+1;return l.eachBefore(Ge)}function Ee(){return ee(this).eachBefore(Oe)}function Ie(e){return e.children}function He(e){return Array.isArray(e)?e[1]:null}function Oe(e){e.data.value!==void 0&&(e.value=e.data.value),e.data=e.data.data}function Ge(e){var a=0;do e.height=a;while((e=e.parent)&&e.height<++a)}function U(e){this.data=e,this.depth=this.height=0,this.parent=null}U.prototype=ee.prototype={constructor:U,count:Te,each:Fe,eachAfter:ke,eachBefore:Ne,find:Ae,sum:_e,sort:ze,path:Me,ancestors:Pe,descendants:Be,leaves:Re,links:De,copy:Ee,[Symbol.iterator]:We};function qe(e){if(typeof e!="function")throw new Error;return e}function G(){return 0}function q(e){return function(){return e}}function Xe(e){e.x0=Math.round(e.x0),e.y0=Math.round(e.y0),e.x1=Math.round(e.x1),e.y1=Math.round(e.y1)}function Ye(e,a,l,n,i){for(var r=e.children,d,p=-1,g=r.length,c=e.value&&(n-a)/e.value;++p<g;)d=r[p],d.y0=l,d.y1=i,d.x0=a,d.x1=a+=d.value*c}function je(e,a,l,n,i){for(var r=e.children,d,p=-1,g=r.length,c=e.value&&(i-l)/e.value;++p<g;)d=r[p],d.x0=a,d.x1=n,d.y0=l,d.y1=l+=d.value*c}var Ke=(1+Math.sqrt(5))/2;function Ue(e,a,l,n,i,r){for(var d=[],p=a.children,g,c,u=0,w=0,s=p.length,b,S,v=a.value,h,m,A,k,P,W,_;u<s;){b=i-l,S=r-n;do h=p[w++].value;while(!h&&w<s);for(m=A=h,W=Math.max(S/b,b/S)/(v*e),_=h*h*W,P=Math.max(A/_,_/m);w<s;++w){if(h+=c=p[w].value,c<m&&(m=c),c>A&&(A=c),_=h*h*W,k=Math.max(A/_,_/m),k>P){h-=c;break}P=k}d.push(g={value:h,dice:b<S,children:p.slice(u,w)}),g.dice?Ye(g,l,n,i,v?n+=S*h/v:r):je(g,l,n,v?l+=b*h/v:i,r),v-=h,u=w}return d}const Ze=function e(a){function l(n,i,r,d,p){Ue(a,n,i,r,d,p)}return l.ratio=function(n){return e((n=+n)>1?n:1)},l}(Ke);function Je(){var e=Ze,a=!1,l=1,n=1,i=[0],r=G,d=G,p=G,g=G,c=G;function u(s){return s.x0=s.y0=0,s.x1=l,s.y1=n,s.eachBefore(w),i=[0],a&&s.eachBefore(Xe),s}function w(s){var b=i[s.depth],S=s.x0+b,v=s.y0+b,h=s.x1-b,m=s.y1-b;h<S&&(S=h=(S+h)/2),m<v&&(v=m=(v+m)/2),s.x0=S,s.y0=v,s.x1=h,s.y1=m,s.children&&(b=i[s.depth+1]=r(s)/2,S+=c(s)-b,v+=d(s)-b,h-=p(s)-b,m-=g(s)-b,h<S&&(S=h=(S+h)/2),m<v&&(v=m=(v+m)/2),e(s,S,v,h,m))}return u.round=function(s){return arguments.length?(a=!!s,u):a},u.size=function(s){return arguments.length?(l=+s[0],n=+s[1],u):[l,n]},u.tile=function(s){return arguments.length?(e=qe(s),u):e},u.padding=function(s){return arguments.length?u.paddingInner(s).paddingOuter(s):u.paddingInner()},u.paddingInner=function(s){return arguments.length?(r=typeof s=="function"?s:q(+s),u):r},u.paddingOuter=function(s){return arguments.length?u.paddingTop(s).paddingRight(s).paddingBottom(s).paddingLeft(s):u.paddingTop()},u.paddingTop=function(s){return arguments.length?(d=typeof s=="function"?s:q(+s),u):d},u.paddingRight=function(s){return arguments.length?(p=typeof s=="function"?s:q(+s),u):p},u.paddingBottom=function(s){return arguments.length?(g=typeof s=="function"?s:q(+s),u):g},u.paddingLeft=function(s){return arguments.length?(c=typeof s=="function"?s:q(+s),u):c},u}var Qe={nodes:[],levels:new Map,outerNodes:[],classes:new Map},V=new Ce(()=>structuredClone(Qe)),et=x(()=>{const e=be,a=ne();return ae({...e.treemap,...a.treemap??{}})},"getConfig"),tt=x(()=>V.records.nodes,"getNodes"),at=x((e,a)=>{const l=V.records;l.nodes.push(e),l.levels.set(e,a),a===0&&l.outerNodes.push(e),a===0&&!l.root&&(l.root=e)},"addNode"),nt=x(()=>({name:"",children:V.records.outerNodes}),"getRoot"),lt=x((e,a)=>{const l=V.records.classes,n=l.get(e)??{id:e,styles:[],textStyles:[]};l.set(e,n);const i=a.replace(/\\,/g,"§§§").replace(/,/g,";").replace(/§§§/g,",").split(";");i&&i.forEach(r=>{ve(r)&&(n!=null&&n.textStyles?n.textStyles.push(r):n.textStyles=[r]),n!=null&&n.styles?n.styles.push(r):n.styles=[r]}),l.set(e,n)},"addClass"),rt=x(()=>V.records.classes,"getClasses"),st=x(e=>{var a;return((a=V.records.classes.get(e))==null?void 0:a.styles)??[]},"getStylesForClass"),ot=x(()=>{xe(),V.reset()},"clear"),Y={getNodes:tt,addNode:at,getRoot:nt,getConfig:et,clear:ot,setAccTitle:me,getAccTitle:ge,setDiagramTitle:fe,getDiagramTitle:he,getAccDescription:pe,setAccDescription:ue,addClass:lt,getClasses:rt,getStylesForClass:st};function le(e){if(!e.length)return[];const a=[],l=[];return e.forEach(n=>{const i={name:n.name,children:n.type==="Leaf"?void 0:[]};for(i.classSelector=n==null?void 0:n.classSelector,n!=null&&n.cssCompiledStyles&&(i.cssCompiledStyles=[n.cssCompiledStyles]),n.type==="Leaf"&&n.value!==void 0&&(i.value=n.value);l.length>0&&l[l.length-1].level>=n.level;)l.pop();if(l.length===0)a.push(i);else{const r=l[l.length-1].node;r.children?r.children.push(i):r.children=[i]}n.type!=="Leaf"&&l.push({node:i,level:n.level})}),a}x(le,"buildHierarchy");var it=x(e=>{we(e,Y);const a=[];for(const i of e.TreemapRows??[])i.$type==="ClassDefStatement"&&Y.addClass(i.className??"",i.styleText??"");for(const i of e.TreemapRows??[]){const r=i.item;if(!r)continue;const d=i.indent?parseInt(i.indent):0,p=ct(r),g=r.classSelector?Y.getStylesForClass(r.classSelector):[],c=g.length>0?g.join(";"):void 0,u={level:d,name:p,type:r.$type,value:r.value,classSelector:r.classSelector,cssCompiledStyles:c};a.push(u)}const l=le(a),n=x((i,r)=>{for(const d of i)Y.addNode(d,r),d.children&&d.children.length>0&&n(d.children,r+1)},"addNodesRecursively");n(l,0)},"populate"),ct=x(e=>e.name?String(e.name):"","getItemName"),dt={parse:x(async e=>{try{const l=await Le("treemap",e);Q.debug("Treemap AST:",l),it(l)}catch(a){throw Q.error("Error parsing treemap:",a),a}},"parse")},ut=10,D=10,X=25,pt=x((e,a,l,n)=>{const i=n.db,r=i.getConfig(),d=r.padding??ut,p=i.getDiagramTitle(),g=i.getRoot(),{themeVariables:c}=ne();if(!g)return;const u=p?30:0,w=ye(a),s=r.nodeWidth?r.nodeWidth*D:960,b=r.nodeHeight?r.nodeHeight*D:500,S=s,v=b+u;w.attr("viewBox",`0 0 ${S} ${v}`),Se(w,v,S,r.useMaxWidth);let h;try{const t=r.valueFormat||",";if(t==="$0,0")h=x(o=>"$"+O(",")(o),"valueFormat");else if(t.startsWith("$")&&t.includes(",")){const o=/\.\d+/.exec(t),f=o?o[0]:"";h=x(C=>"$"+O(","+f)(C),"valueFormat")}else if(t.startsWith("$")){const o=t.substring(1);h=x(f=>"$"+O(o||"")(f),"valueFormat")}else h=O(t)}catch(t){Q.error("Error creating format function:",t),h=O(",")}const m=J().range(["transparent",c.cScale0,c.cScale1,c.cScale2,c.cScale3,c.cScale4,c.cScale5,c.cScale6,c.cScale7,c.cScale8,c.cScale9,c.cScale10,c.cScale11]),A=J().range(["transparent",c.cScalePeer0,c.cScalePeer1,c.cScalePeer2,c.cScalePeer3,c.cScalePeer4,c.cScalePeer5,c.cScalePeer6,c.cScalePeer7,c.cScalePeer8,c.cScalePeer9,c.cScalePeer10,c.cScalePeer11]),k=J().range([c.cScaleLabel0,c.cScaleLabel1,c.cScaleLabel2,c.cScaleLabel3,c.cScaleLabel4,c.cScaleLabel5,c.cScaleLabel6,c.cScaleLabel7,c.cScaleLabel8,c.cScaleLabel9,c.cScaleLabel10,c.cScaleLabel11]);p&&w.append("text").attr("x",S/2).attr("y",u/2).attr("class","treemapTitle").attr("text-anchor","middle").attr("dominant-baseline","middle").text(p);const P=w.append("g").attr("transform",`translate(0, ${u})`).attr("class","treemapContainer"),W=ee(g).sum(t=>t.value??0).sort((t,o)=>(o.value??0)-(t.value??0)),te=Je().size([s,b]).paddingTop(t=>t.children&&t.children.length>0?X+D:0).paddingInner(d).paddingLeft(t=>t.children&&t.children.length>0?D:0).paddingRight(t=>t.children&&t.children.length>0?D:0).paddingBottom(t=>t.children&&t.children.length>0?D:0).round(!0)(W),re=te.descendants().filter(t=>t.children&&t.children.length>0),E=P.selectAll(".treemapSection").data(re).enter().append("g").attr("class","treemapSection").attr("transform",t=>`translate(${t.x0},${t.y0})`);E.append("rect").attr("width",t=>t.x1-t.x0).attr("height",X).attr("class","treemapSectionHeader").attr("fill","none").attr("fill-opacity",.6).attr("stroke-width",.6).attr("style",t=>t.depth===0?"display: none;":""),E.append("clipPath").attr("id",(t,o)=>`clip-section-${a}-${o}`).append("rect").attr("width",t=>Math.max(0,t.x1-t.x0-12)).attr("height",X),E.append("rect").attr("width",t=>t.x1-t.x0).attr("height",t=>t.y1-t.y0).attr("class",(t,o)=>`treemapSection section${o}`).attr("fill",t=>m(t.data.name)).attr("fill-opacity",.6).attr("stroke",t=>A(t.data.name)).attr("stroke-width",2).attr("stroke-opacity",.4).attr("style",t=>{if(t.depth===0)return"display: none;";const o=R({cssCompiledStyles:t.data.cssCompiledStyles});return o.nodeStyles+";"+o.borderStyles.join(";")}),E.append("text").attr("class","treemapSectionLabel").attr("x",6).attr("y",X/2).attr("dominant-baseline","middle").text(t=>t.depth===0?"":t.data.name).attr("font-weight","bold").attr("style",t=>{if(t.depth===0)return"display: none;";const o="dominant-baseline: middle; font-size: 12px; fill:"+k(t.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",f=R({cssCompiledStyles:t.data.cssCompiledStyles});return o+f.labelStyles.replace("color:","fill:")}).each(function(t){if(t.depth===0)return;const o=K(this),f=t.data.name;o.text(f);const C=t.x1-t.x0,T=6;let F;r.showValues!==!1&&t.value?F=C-10-30-10-T:F=C-T-6;const N=Math.max(15,F),y=o.node();if(y.getComputedTextLength()>N){const L="...";let $=f;for(;$.length>0;){if($=f.substring(0,$.length-1),$.length===0){o.text(L),y.getComputedTextLength()>N&&o.text("");break}if(o.text($+L),y.getComputedTextLength()<=N)break}}}),r.showValues!==!1&&E.append("text").attr("class","treemapSectionValue").attr("x",t=>t.x1-t.x0-10).attr("y",X/2).attr("text-anchor","end").attr("dominant-baseline","middle").text(t=>t.value?h(t.value):"").attr("font-style","italic").attr("style",t=>{if(t.depth===0)return"display: none;";const o="text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:"+k(t.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",f=R({cssCompiledStyles:t.data.cssCompiledStyles});return o+f.labelStyles.replace("color:","fill:")});const se=te.leaves(),j=P.selectAll(".treemapLeafGroup").data(se).enter().append("g").attr("class",(t,o)=>`treemapNode treemapLeafGroup leaf${o}${t.data.classSelector?` ${t.data.classSelector}`:""}x`).attr("transform",t=>`translate(${t.x0},${t.y0})`);j.append("rect").attr("width",t=>t.x1-t.x0).attr("height",t=>t.y1-t.y0).attr("class","treemapLeaf").attr("fill",t=>t.parent?m(t.parent.data.name):m(t.data.name)).attr("style",t=>R({cssCompiledStyles:t.data.cssCompiledStyles}).nodeStyles).attr("fill-opacity",.3).attr("stroke",t=>t.parent?m(t.parent.data.name):m(t.data.name)).attr("stroke-width",3),j.append("clipPath").attr("id",(t,o)=>`clip-${a}-${o}`).append("rect").attr("width",t=>Math.max(0,t.x1-t.x0-4)).attr("height",t=>Math.max(0,t.y1-t.y0-4)),j.append("text").attr("class","treemapLabel").attr("x",t=>(t.x1-t.x0)/2).attr("y",t=>(t.y1-t.y0)/2).attr("style",t=>{const o="text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:"+k(t.data.name)+";",f=R({cssCompiledStyles:t.data.cssCompiledStyles});return o+f.labelStyles.replace("color:","fill:")}).attr("clip-path",(t,o)=>`url(#clip-${a}-${o})`).text(t=>t.data.name).each(function(t){const o=K(this),f=t.x1-t.x0,C=t.y1-t.y0,T=o.node(),F=4,B=f-2*F,N=C-2*F;if(B<10||N<10){o.style("display","none");return}let y=parseInt(o.style("font-size"),10);const z=8,L=28,$=.6,M=6,I=2;for(;T.getComputedTextLength()>B&&y>z;)y--,o.style("font-size",`${y}px`);let H=Math.max(M,Math.min(L,Math.round(y*$))),Z=y+I+H;for(;Z>N&&y>z&&(y--,H=Math.max(M,Math.min(L,Math.round(y*$))),!(H<M&&y===z));)o.style("font-size",`${y}px`),Z=y+I+H;o.style("font-size",`${y}px`),(T.getComputedTextLength()>B||y<z||N<y)&&o.style("display","none")}),r.showValues!==!1&&j.append("text").attr("class","treemapValue").attr("x",o=>(o.x1-o.x0)/2).attr("y",function(o){return(o.y1-o.y0)/2}).attr("style",o=>{const f="text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:"+k(o.data.name)+";",C=R({cssCompiledStyles:o.data.cssCompiledStyles});return f+C.labelStyles.replace("color:","fill:")}).attr("clip-path",(o,f)=>`url(#clip-${a}-${f})`).text(o=>o.value?h(o.value):"").each(function(o){const f=K(this),C=this.parentNode;if(!C){f.style("display","none");return}const T=K(C).select(".treemapLabel");if(T.empty()||T.style("display")==="none"){f.style("display","none");return}const F=parseFloat(T.style("font-size")),B=28,N=.6,y=6,z=2,L=Math.max(y,Math.min(B,Math.round(F*N)));f.style("font-size",`${L}px`);const M=(o.y1-o.y0)/2+F/2+z;f.attr("y",M);const I=o.x1-o.x0,ie=o.y1-o.y0-4,ce=I-2*4;f.node().getComputedTextLength()>ce||M+L>ie||L<y?f.style("display","none"):f.style("display",null)});const oe=r.diagramPadding??8;de(w,oe,"flowchart",(r==null?void 0:r.useMaxWidth)||!1)},"draw"),ht=x(function(e,a){return a.db.getClasses()},"getClasses"),ft={draw:pt,getClasses:ht},gt={sectionStrokeColor:"black",sectionStrokeWidth:"1",sectionFillColor:"#efefef",leafStrokeColor:"black",leafStrokeWidth:"1",leafFillColor:"#efefef",labelColor:"black",labelFontSize:"12px",valueFontSize:"10px",valueColor:"black",titleColor:"black",titleFontSize:"14px"},mt=x(({treemap:e}={})=>{const a=ae(gt,e);return`
  .treemapNode.section {
    stroke: ${a.sectionStrokeColor};
    stroke-width: ${a.sectionStrokeWidth};
    fill: ${a.sectionFillColor};
  }
  .treemapNode.leaf {
    stroke: ${a.leafStrokeColor};
    stroke-width: ${a.leafStrokeWidth};
    fill: ${a.leafFillColor};
  }
  .treemapLabel {
    fill: ${a.labelColor};
    font-size: ${a.labelFontSize};
  }
  .treemapValue {
    fill: ${a.valueColor};
    font-size: ${a.valueFontSize};
  }
  .treemapTitle {
    fill: ${a.titleColor};
    font-size: ${a.titleFontSize};
  }
  `},"getStyles"),yt=mt,zt={parser:dt,db:Y,renderer:ft,styles:yt};export{zt as diagram};

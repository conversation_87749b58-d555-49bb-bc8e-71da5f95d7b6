import{s as a,c as s,a as e,C as t}from"./chunk-QEP2MXWD-BNewYZiw.js";import{_ as i}from"./index-p8SKoq5x.js";import"./chunk-E2GYISFI-RzeHN6-1.js";import"./chunk-BFAMUDN2-BNVTnpcL.js";import"./chunk-SKB7J2MH-DOkrMxx7.js";import"./vendor-BEryHLmj.js";import"./pdfjs-CcP0jMWS.js";var n={parser:e,get db(){return new t},renderer:s,styles:a,init:i(r=>{r.class||(r.class={}),r.class.arrowMarkerAbsolute=r.arrowMarkerAbsolute},"init")};export{n as diagram};

@echo off
echo ========================================
echo      WeMa IA - Tests Automatiques
echo ========================================
echo.

echo 🔧 Test 1: Verification Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python non trouve
    goto :error
)
echo ✅ Python OK

echo.
echo 🔧 Test 2: Verification dependances...
cd backend
python -c "import fastapi, uvicorn, requests" >nul 2>&1
if errorlevel 1 (
    echo ❌ Dependances manquantes
    goto :error
)
echo ✅ Dependances OK

echo.
echo 🔧 Test 3: Test backend (demarrage rapide)...
start /B python main.py >nul 2>&1
timeout /t 5 /nobreak >nul

echo.
echo 🔧 Test 4: Test API Health...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5001/health' -UseBasicParsing -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ❌ API non accessible
    goto :cleanup
)
echo ✅ API Health OK

echo.
echo 🔧 Test 5: Test recherche internet...
powershell -Command "$body = @{query='test'; search_type='general'; max_results=1} | ConvertTo-Json; try { $response = Invoke-WebRequest -Uri 'http://localhost:5001/search/internet' -Method POST -Body $body -ContentType 'application/json' -UseBasicParsing -TimeoutSec 10; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ❌ Recherche internet echouee
    goto :cleanup
)
echo ✅ Recherche Internet OK

echo.
echo 🔧 Test 6: Test serveurs centraux...
powershell -Command "try { Invoke-WebRequest -Uri 'http://***********:8888' -UseBasicParsing -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ SearXNG Central non accessible
) else (
    echo ✅ SearXNG Central OK
)

powershell -Command "try { Invoke-WebRequest -Uri 'http://***********:1234/v1/models' -UseBasicParsing -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ LM Studio Central non accessible
) else (
    echo ✅ LM Studio Central OK
)

echo.
echo 🔧 Test 7: Verification frontend...
cd ..\frontend
if not exist "index.html" (
    echo ❌ Frontend manquant
    goto :cleanup
)
echo ✅ Frontend OK

:cleanup
echo.
echo 🧹 Nettoyage...
taskkill /F /IM python.exe >nul 2>&1
cd ..

echo.
echo ========================================
echo      ✅ TESTS TERMINES
echo ========================================
echo.
echo 🎯 WeMa IA est pret pour utilisation !
echo 🚀 Demarrer avec: start-wema-new.bat
echo.
goto :end

:error
echo.
echo ========================================
echo      ❌ ERREUR DETECTEE
echo ========================================
echo.
echo Veuillez corriger les erreurs avant utilisation.
echo.

:end
pause

import{aV as j,aW as p,aX as w,aY as q,aZ as k}from"./index-p8SKoq5x.js";import{i as D}from"./init-Gi6I4Gst.js";import{e as g,f as F,a as V,b as z}from"./defaultLocale-C4B-KCzX.js";function B(n,r){r||(r=[]);var t=n?Math.min(r.length,n.length):0,e=r.slice(),u;return function(f){for(u=0;u<t;++u)e[u]=n[u]*(1-f)+r[u]*f;return e}}function I(n){return ArrayBuffer.isView(n)&&!(n instanceof DataView)}function P(n,r){var t=r?r.length:0,e=n?Math.min(t,n.length):0,u=new Array(e),f=new Array(t),a;for(a=0;a<e;++a)u[a]=d(n[a],r[a]);for(;a<t;++a)f[a]=r[a];return function(o){for(a=0;a<e;++a)f[a]=u[a](o);return f}}function $(n,r){var t=new Date;return n=+n,r=+r,function(e){return t.setTime(n*(1-e)+r*e),t}}function x(n,r){var t={},e={},u;(n===null||typeof n!="object")&&(n={}),(r===null||typeof r!="object")&&(r={});for(u in r)u in n?t[u]=d(n[u],r[u]):e[u]=r[u];return function(f){for(u in t)e[u]=t[u](f);return e}}function d(n,r){var t=typeof r,e;return r==null||t==="boolean"?j(r):(t==="number"?p:t==="string"?(e=k(r))?(r=e,w):q:r instanceof k?w:r instanceof Date?$:I(r)?B:Array.isArray(r)?P:typeof r.valueOf!="function"&&typeof r.toString!="function"||isNaN(r)?x:p)(n,r)}function O(n,r){return n=+n,r=+r,function(t){return Math.round(n*(1-t)+r*t)}}function M(n,r){return n==null||r==null?NaN:n<r?-1:n>r?1:n>=r?0:NaN}function T(n,r){return n==null||r==null?NaN:r<n?-1:r>n?1:r>=n?0:NaN}function R(n){let r,t,e;n.length!==2?(r=M,t=(o,c)=>M(n(o),c),e=(o,c)=>n(o)-c):(r=n===M||n===T?n:W,t=n,e=n);function u(o,c,i=0,h=o.length){if(i<h){if(r(c,c)!==0)return h;do{const l=i+h>>>1;t(o[l],c)<0?i=l+1:h=l}while(i<h)}return i}function f(o,c,i=0,h=o.length){if(i<h){if(r(c,c)!==0)return h;do{const l=i+h>>>1;t(o[l],c)<=0?i=l+1:h=l}while(i<h)}return i}function a(o,c,i=0,h=o.length){const l=u(o,c,i,h-1);return l>i&&e(o[l-1],c)>-e(o[l],c)?l-1:l}return{left:u,center:a,right:f}}function W(){return 0}function X(n){return n===null?NaN:+n}const Y=R(M),Z=Y.right;R(X).center;const C=Math.sqrt(50),E=Math.sqrt(10),G=Math.sqrt(2);function v(n,r,t){const e=(r-n)/Math.max(0,t),u=Math.floor(Math.log10(e)),f=e/Math.pow(10,u),a=f>=C?10:f>=E?5:f>=G?2:1;let o,c,i;return u<0?(i=Math.pow(10,-u)/a,o=Math.round(n*i),c=Math.round(r*i),o/i<n&&++o,c/i>r&&--c,i=-i):(i=Math.pow(10,u)*a,o=Math.round(n/i),c=Math.round(r/i),o*i<n&&++o,c*i>r&&--c),c<o&&.5<=t&&t<2?v(n,r,t*2):[o,c,i]}function H(n,r,t){if(r=+r,n=+n,t=+t,!(t>0))return[];if(n===r)return[n];const e=r<n,[u,f,a]=e?v(r,n,t):v(n,r,t);if(!(f>=u))return[];const o=f-u+1,c=new Array(o);if(e)if(a<0)for(let i=0;i<o;++i)c[i]=(f-i)/-a;else for(let i=0;i<o;++i)c[i]=(f-i)*a;else if(a<0)for(let i=0;i<o;++i)c[i]=(u+i)/-a;else for(let i=0;i<o;++i)c[i]=(u+i)*a;return c}function y(n,r,t){return r=+r,n=+n,t=+t,v(n,r,t)[2]}function J(n,r,t){r=+r,n=+n,t=+t;const e=r<n,u=e?y(r,n,t):y(n,r,t);return(e?-1:1)*(u<0?1/-u:u)}function K(n){return Math.max(0,-g(Math.abs(n)))}function L(n,r){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(g(r)/3)))*3-g(Math.abs(n)))}function Q(n,r){return n=Math.abs(n),r=Math.abs(r)-n,Math.max(0,g(r)-g(n))+1}function U(n){return function(){return n}}function _(n){return+n}var A=[0,1];function m(n){return n}function N(n,r){return(r-=n=+n)?function(t){return(t-n)/r}:U(isNaN(r)?NaN:.5)}function b(n,r){var t;return n>r&&(t=n,n=r,r=t),function(e){return Math.max(n,Math.min(r,e))}}function nn(n,r,t){var e=n[0],u=n[1],f=r[0],a=r[1];return u<e?(e=N(u,e),f=t(a,f)):(e=N(e,u),f=t(f,a)),function(o){return f(e(o))}}function rn(n,r,t){var e=Math.min(n.length,r.length)-1,u=new Array(e),f=new Array(e),a=-1;for(n[e]<n[0]&&(n=n.slice().reverse(),r=r.slice().reverse());++a<e;)u[a]=N(n[a],n[a+1]),f[a]=t(r[a],r[a+1]);return function(o){var c=Z(n,o,1,e)-1;return f[c](u[c](o))}}function en(n,r){return r.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown())}function tn(){var n=A,r=A,t=d,e,u,f,a=m,o,c,i;function h(){var s=Math.min(n.length,r.length);return a!==m&&(a=b(n[0],n[s-1])),o=s>2?rn:nn,c=i=null,l}function l(s){return s==null||isNaN(s=+s)?f:(c||(c=o(n.map(e),r,t)))(e(a(s)))}return l.invert=function(s){return a(u((i||(i=o(r,n.map(e),p)))(s)))},l.domain=function(s){return arguments.length?(n=Array.from(s,_),h()):n.slice()},l.range=function(s){return arguments.length?(r=Array.from(s),h()):r.slice()},l.rangeRound=function(s){return r=Array.from(s),t=O,h()},l.clamp=function(s){return arguments.length?(a=s?!0:m,h()):a!==m},l.interpolate=function(s){return arguments.length?(t=s,h()):t},l.unknown=function(s){return arguments.length?(f=s,l):f},function(s,S){return e=s,u=S,h()}}function un(){return tn()(m,m)}function an(n,r,t,e){var u=J(n,r,t),f;switch(e=F(e??",f"),e.type){case"s":{var a=Math.max(Math.abs(n),Math.abs(r));return e.precision==null&&!isNaN(f=L(u,a))&&(e.precision=f),V(e,a)}case"":case"e":case"g":case"p":case"r":{e.precision==null&&!isNaN(f=Q(u,Math.max(Math.abs(n),Math.abs(r))))&&(e.precision=f-(e.type==="e"));break}case"f":case"%":{e.precision==null&&!isNaN(f=K(u))&&(e.precision=f-(e.type==="%")*2);break}}return z(e)}function on(n){var r=n.domain;return n.ticks=function(t){var e=r();return H(e[0],e[e.length-1],t??10)},n.tickFormat=function(t,e){var u=r();return an(u[0],u[u.length-1],t??10,e)},n.nice=function(t){t==null&&(t=10);var e=r(),u=0,f=e.length-1,a=e[u],o=e[f],c,i,h=10;for(o<a&&(i=a,a=o,o=i,i=u,u=f,f=i);h-- >0;){if(i=y(a,o,t),i===c)return e[u]=a,e[f]=o,r(e);if(i>0)a=Math.floor(a/i)*i,o=Math.ceil(o/i)*i;else if(i<0)a=Math.ceil(a*i)/i,o=Math.floor(o*i)/i;else break;c=i}return n},n}function fn(){var n=un();return n.copy=function(){return en(n,fn())},D.apply(n,arguments),on(n)}export{en as a,R as b,un as c,fn as l,J as t};

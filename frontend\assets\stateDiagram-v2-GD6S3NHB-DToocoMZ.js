import{s as r,b as e,a,S as s}from"./chunk-6OLS64BW-BiaFxRNK.js";import{_ as i}from"./index-p8SKoq5x.js";import"./chunk-BFAMUDN2-BNVTnpcL.js";import"./chunk-SKB7J2MH-DOkrMxx7.js";import"./vendor-BEryHLmj.js";import"./pdfjs-CcP0jMWS.js";var u={parser:a,get db(){return new s(2)},renderer:e,styles:r,init:i(t=>{t.state||(t.state={}),t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute},"init")};export{u as diagram};

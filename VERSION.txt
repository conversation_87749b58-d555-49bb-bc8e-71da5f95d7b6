WeMa IA - Version Portable
==========================

Version: 1.0.0
Date: 2025-07-09
Type: FINALE - PRODUCTION READY

CHANGELOG
---------

v1.0.0 (2025-07-09) - VERSION FINALE
- ✅ Architecture distribuée client/serveur opérationnelle
- ✅ Recherche internet déléguée au serveur central (***********:8888)
- ✅ Chat IA avec LM Studio central (***********:1234)
- ✅ Interface 100% française (WeMa IA branding)
- ✅ OCR documents (PDF, Word, PowerPoint)
- ✅ Streaming des réponses en temps réel
- ✅ Compression intelligente du contexte
- ✅ Sessions isolées par utilisateur
- ✅ Scripts de finalisation et démarrage automatiques
- ✅ Version complètement autonome et portable

CONFIGURATION TECHNIQUE
-----------------------
- Backend: Python FastAPI (port 5001)
- Frontend: Interface web compilée (port 8080)
- IA: qwen3-14b par défaut, qwen3-32b disponible
- Recherche: SearXNG central avec scraping web
- OCR: Moteur premium avec support tables
- Base de données: SQLite locale pour sessions

SERVEURS CENTRAUX
-----------------
- LM Studio: ***********:1234
- SearXNG: ***********:8888
- Architecture: Client local + Serveurs centraux

DÉPLOIEMENT
-----------
- Copier le dossier dist-wema-portable
- Exécuter finalize-wema.bat (première fois)
- Démarrer avec start-wema-new.bat
- Accéder via http://localhost:8080

PRÉREQUIS
---------
- Python 3.8+ avec pip
- Connexion réseau vers serveurs centraux
- Windows 10/11 (testé)

NOTES
-----
Cette version est la RÉFÉRENCE FINALE pour WeMa IA.
Toutes les fonctionnalités sont opérationnelles et testées.
Prête pour distribution en entreprise.

Développé par: WeMa IA Team
Contact: Support technique disponible

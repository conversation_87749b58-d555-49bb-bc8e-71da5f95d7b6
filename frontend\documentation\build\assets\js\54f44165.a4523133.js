"use strict";(self.webpackChunkdocumentation=self.webpackChunkdocumentation||[]).push([[924],{5287:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>a,default:()=>h,frontMatter:()=>t,metadata:()=>s,toc:()=>d});const s=JSON.parse('{"id":"getting-started/installation","title":"Installation","description":"Install Clara on your system","source":"@site/docs/getting-started/installation.md","sourceDirName":"getting-started","slug":"/getting-started/installation","permalink":"/getting-started/installation","draft":false,"unlisted":false,"tags":[],"version":"current","sidebarPosition":2,"frontMatter":{"title":"Installation","description":"Install Clara on your system","sidebar_position":2},"sidebar":"docsSidebar","previous":{"title":"Introduction to <PERSON>","permalink":"/getting-started/introduction"},"next":{"title":"Quick Start Guide","permalink":"/getting-started/quick-start"}}');var l=i(4848),r=i(8453);const t={title:"Installation",description:"Install Clara on your system",sidebar_position:2},a="Installation Guide",o={},d=[{value:"System Requirements",id:"system-requirements",level:2},{value:"Minimum Requirements",id:"minimum-requirements",level:3},{value:"Recommended Requirements",id:"recommended-requirements",level:3},{value:"Download &amp; Install",id:"download--install",level:2},{value:"Option 1: Pre-built Releases (Recommended)",id:"option-1-pre-built-releases-recommended",level:3},{value:"Windows",id:"windows",level:4},{value:"macOS",id:"macos",level:4},{value:"Linux",id:"linux",level:4},{value:"Option 2: Package Managers",id:"option-2-package-managers",level:3},{value:"Windows (Chocolatey)",id:"windows-chocolatey",level:4},{value:"macOS (Homebrew)",id:"macos-homebrew",level:4},{value:"Linux (Snap)",id:"linux-snap",level:4},{value:"Development Installation",id:"development-installation",level:2},{value:"Prerequisites",id:"prerequisites",level:3},{value:"Build from Source",id:"build-from-source",level:3},{value:"AI Provider Setup",id:"ai-provider-setup",level:2},{value:"OpenAI",id:"openai",level:3},{value:"Anthropic",id:"anthropic",level:3},{value:"Ollama (Local)",id:"ollama-local",level:3},{value:"Verification",id:"verification",level:2},{value:"Troubleshooting",id:"troubleshooting",level:2},{value:"Common Issues",id:"common-issues",level:3},{value:"Installation Fails",id:"installation-fails",level:4},{value:"Clara Won&#39;t Start",id:"clara-wont-start",level:4},{value:"AI Provider Issues",id:"ai-provider-issues",level:4},{value:"Performance Issues",id:"performance-issues",level:4},{value:"Getting Help",id:"getting-help",level:3},{value:"Next Steps",id:"next-steps",level:2}];function c(e){const n={a:"a",admonition:"admonition",code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",hr:"hr",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n.header,{children:(0,l.jsx)(n.h1,{id:"installation-guide",children:"Installation Guide"})}),"\n",(0,l.jsx)(n.p,{children:"Clara is available for Windows, macOS, and Linux. Choose the installation method that works best for you."}),"\n",(0,l.jsx)(n.h2,{id:"system-requirements",children:"System Requirements"}),"\n",(0,l.jsx)(n.h3,{id:"minimum-requirements",children:"Minimum Requirements"}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"OS"}),": Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"RAM"}),": 4GB (8GB recommended)"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Storage"}),": 2GB free space"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Node.js"}),": Version 18+ (for development)"]}),"\n"]}),"\n",(0,l.jsx)(n.h3,{id:"recommended-requirements",children:"Recommended Requirements"}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"RAM"}),": 16GB+ (for local AI models)"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"GPU"}),": NVIDIA GPU with 8GB+ VRAM (for image generation)"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Storage"}),": 10GB+ free space (for models and data)"]}),"\n"]}),"\n",(0,l.jsx)(n.h2,{id:"download--install",children:"Download & Install"}),"\n",(0,l.jsx)(n.h3,{id:"option-1-pre-built-releases-recommended",children:"Option 1: Pre-built Releases (Recommended)"}),"\n",(0,l.jsx)(n.p,{children:"Download the latest release for your platform:"}),"\n",(0,l.jsx)(n.h4,{id:"windows",children:"Windows"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsxs)(n.li,{children:["Download ",(0,l.jsx)(n.code,{children:"Clara-Setup-x.x.x.exe"})," from ",(0,l.jsx)(n.a,{href:"https://github.com/your-org/clara/releases",children:"GitHub Releases"})]}),"\n",(0,l.jsx)(n.li,{children:"Run the installer as Administrator"}),"\n",(0,l.jsx)(n.li,{children:"Follow the installation wizard"}),"\n",(0,l.jsx)(n.li,{children:"Launch Clara from the Start Menu"}),"\n"]}),"\n",(0,l.jsx)(n.h4,{id:"macos",children:"macOS"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsxs)(n.li,{children:["Download ",(0,l.jsx)(n.code,{children:"Clara-x.x.x.dmg"})," from ",(0,l.jsx)(n.a,{href:"https://github.com/your-org/clara/releases",children:"GitHub Releases"})]}),"\n",(0,l.jsx)(n.li,{children:"Open the DMG file"}),"\n",(0,l.jsx)(n.li,{children:"Drag Clara to your Applications folder"}),"\n",(0,l.jsx)(n.li,{children:"Launch Clara from Applications"}),"\n"]}),"\n",(0,l.jsx)(n.admonition,{title:"macOS Security",type:"tip",children:(0,l.jsxs)(n.p,{children:["If you see a security warning, go to ",(0,l.jsx)(n.strong,{children:"System Preferences > Security & Privacy"}),' and click "Open Anyway"']})}),"\n",(0,l.jsx)(n.h4,{id:"linux",children:"Linux"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsxs)(n.li,{children:["Download ",(0,l.jsx)(n.code,{children:"Clara-x.x.x.AppImage"})," from ",(0,l.jsx)(n.a,{href:"https://github.com/your-org/clara/releases",children:"GitHub Releases"})]}),"\n",(0,l.jsxs)(n.li,{children:["Make it executable: ",(0,l.jsx)(n.code,{children:"chmod +x Clara-x.x.x.AppImage"})]}),"\n",(0,l.jsxs)(n.li,{children:["Run: ",(0,l.jsx)(n.code,{children:"./Clara-x.x.x.AppImage"})]}),"\n"]}),"\n",(0,l.jsx)(n.h3,{id:"option-2-package-managers",children:"Option 2: Package Managers"}),"\n",(0,l.jsx)(n.h4,{id:"windows-chocolatey",children:"Windows (Chocolatey)"}),"\n",(0,l.jsx)(n.pre,{children:(0,l.jsx)(n.code,{className:"language-bash",children:"choco install clara\n"})}),"\n",(0,l.jsx)(n.h4,{id:"macos-homebrew",children:"macOS (Homebrew)"}),"\n",(0,l.jsx)(n.pre,{children:(0,l.jsx)(n.code,{className:"language-bash",children:"brew install --cask clara\n"})}),"\n",(0,l.jsx)(n.h4,{id:"linux-snap",children:"Linux (Snap)"}),"\n",(0,l.jsx)(n.pre,{children:(0,l.jsx)(n.code,{className:"language-bash",children:"sudo snap install clara\n"})}),"\n",(0,l.jsx)(n.h2,{id:"development-installation",children:"Development Installation"}),"\n",(0,l.jsx)(n.p,{children:"For developers who want to build from source:"}),"\n",(0,l.jsx)(n.h3,{id:"prerequisites",children:"Prerequisites"}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Node.js"})," 18+"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"npm"})," or ",(0,l.jsx)(n.strong,{children:"yarn"})]}),"\n",(0,l.jsx)(n.li,{children:(0,l.jsx)(n.strong,{children:"Git"})}),"\n"]}),"\n",(0,l.jsx)(n.h3,{id:"build-from-source",children:"Build from Source"}),"\n",(0,l.jsx)(n.pre,{children:(0,l.jsx)(n.code,{className:"language-bash",children:"# Clone the repository\ngit clone https://github.com/your-org/clara.git\ncd clara\n\n# Install dependencies\nnpm install\n\n# Start development server\nnpm run dev\n\n# Or build for production\nnpm run build\nnpm run electron:build\n"})}),"\n",(0,l.jsx)(n.h2,{id:"ai-provider-setup",children:"AI Provider Setup"}),"\n",(0,l.jsx)(n.p,{children:"Clara requires at least one AI provider to function. Set up your preferred provider:"}),"\n",(0,l.jsx)(n.h3,{id:"openai",children:"OpenAI"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsxs)(n.li,{children:["Get an API key from ",(0,l.jsx)(n.a,{href:"https://platform.openai.com/api-keys",children:"OpenAI"})]}),"\n",(0,l.jsxs)(n.li,{children:["Add to Clara settings: ",(0,l.jsx)(n.code,{children:"sk-your-openai-key-here"})]}),"\n"]}),"\n",(0,l.jsx)(n.h3,{id:"anthropic",children:"Anthropic"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsxs)(n.li,{children:["Get an API key from ",(0,l.jsx)(n.a,{href:"https://console.anthropic.com/",children:"Anthropic"})]}),"\n",(0,l.jsxs)(n.li,{children:["Add to Clara settings: ",(0,l.jsx)(n.code,{children:"sk-ant-your-anthropic-key-here"})]}),"\n"]}),"\n",(0,l.jsx)(n.h3,{id:"ollama-local",children:"Ollama (Local)"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsxs)(n.li,{children:["Install Ollama: ",(0,l.jsx)(n.code,{children:"curl -fsSL https://ollama.ai/install.sh | sh"})]}),"\n",(0,l.jsxs)(n.li,{children:["Pull a model: ",(0,l.jsx)(n.code,{children:"ollama pull llama2"})]}),"\n",(0,l.jsx)(n.li,{children:"Clara will auto-detect local Ollama"}),"\n"]}),"\n",(0,l.jsx)(n.h2,{id:"verification",children:"Verification"}),"\n",(0,l.jsx)(n.p,{children:"After installation, verify Clara is working:"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsx)(n.li,{children:(0,l.jsx)(n.strong,{children:"Launch Clara"})}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Complete onboarding"})," - Enter your name and configure AI provider"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Test AI Assistant"}),' - Send a message: "Hello Clara!"']}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Check features"})," - Try image generation and agent studio"]}),"\n"]}),"\n",(0,l.jsx)(n.h2,{id:"troubleshooting",children:"Troubleshooting"}),"\n",(0,l.jsx)(n.h3,{id:"common-issues",children:"Common Issues"}),"\n",(0,l.jsx)(n.h4,{id:"installation-fails",children:"Installation Fails"}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Windows"}),": Run installer as Administrator"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"macOS"}),": Check Security & Privacy settings"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Linux"}),": Ensure AppImage has execute permissions"]}),"\n"]}),"\n",(0,l.jsx)(n.h4,{id:"clara-wont-start",children:"Clara Won't Start"}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsx)(n.li,{children:"Check system requirements"}),"\n",(0,l.jsx)(n.li,{children:"Verify Node.js version (18+)"}),"\n",(0,l.jsx)(n.li,{children:"Clear application data and restart"}),"\n"]}),"\n",(0,l.jsx)(n.h4,{id:"ai-provider-issues",children:"AI Provider Issues"}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsx)(n.li,{children:"Verify API keys are correct"}),"\n",(0,l.jsx)(n.li,{children:"Check internet connectivity"}),"\n",(0,l.jsx)(n.li,{children:"Ensure sufficient API credits"}),"\n"]}),"\n",(0,l.jsx)(n.h4,{id:"performance-issues",children:"Performance Issues"}),"\n",(0,l.jsxs)(n.ul,{children:["\n",(0,l.jsx)(n.li,{children:"Close unnecessary applications"}),"\n",(0,l.jsx)(n.li,{children:"Increase system RAM"}),"\n",(0,l.jsx)(n.li,{children:"Use local models (Ollama) for better privacy"}),"\n"]}),"\n",(0,l.jsx)(n.h3,{id:"getting-help",children:"Getting Help"}),"\n",(0,l.jsx)(n.p,{children:"If you encounter issues:"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Check logs"})," - Look in Clara's debug console"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Search issues"})," - Check ",(0,l.jsx)(n.a,{href:"https://github.com/your-org/clara/issues",children:"GitHub Issues"})]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Report bugs"})," - Create a new issue with details"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:"Community"})," - Join our community discussions"]}),"\n"]}),"\n",(0,l.jsx)(n.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,l.jsx)(n.p,{children:"Once Clara is installed:"}),"\n",(0,l.jsxs)(n.ol,{children:["\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:(0,l.jsx)(n.a,{href:"/getting-started/quick-start",children:"Quick Start Guide"})})," - Get up and running"]}),"\n",(0,l.jsxs)(n.li,{children:[(0,l.jsx)(n.strong,{children:(0,l.jsx)(n.a,{href:"/getting-started/introduction",children:"Introduction"})})," - Learn about Clara's features"]}),"\n"]}),"\n",(0,l.jsx)(n.hr,{}),"\n",(0,l.jsxs)(n.p,{children:[(0,l.jsx)(n.strong,{children:"Ready to get started?"})," Head to the ",(0,l.jsx)(n.a,{href:"/getting-started/quick-start",children:"Quick Start Guide"})," to begin your Clara journey!"]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,l.jsx)(n,{...e,children:(0,l.jsx)(c,{...e})}):c(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>t,x:()=>a});var s=i(6540);const l={},r=s.createContext(l);function t(e){const n=s.useContext(r);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(l):e.components||l:t(e.components),s.createElement(r.Provider,{value:n},e.children)}}}]);